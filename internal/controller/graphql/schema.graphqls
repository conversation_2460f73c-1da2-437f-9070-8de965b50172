scalar Time

directive @auth on FIELD_DEFINITION
directive @adminAuth on FIELD_DEFINITION

type Query {
  # Get referral snapshot
  referralSnapshot: ReferralSnapshot @auth

  # Get all agent levels
  agentLevels: [AgentLevel!]! @auth

  # Get specific agent level by ID
  agentLevel(id: Int!): AgentLevel @auth

  # Get user level information (current level, volume, next level requirements)
  userLevelInfo: UserLevelInfoResponse! @auth

  # Get data overview with charts
  dataOverview(input: DataOverviewInput!): DataOverviewWithSummary! @auth

  # Get transaction data (all, meme, contract)
  transactionData(input: TransactionDataInput!): TransactionDataResponse! @auth

  # Get user invitation record
  invitationRecords: InvitationRecordResponse! @auth

  # Get withdrawal records for invitation rewards
  withdrawalRecords: WithdrawalRecordResponse! @auth

  # Get invitation summary (invited user count and trading user count)
  invitationSummary: InvitationSummaryResponse! @auth

  # Get invitation list with filtering and pagination
  invitationList(input: InvitationListRequest!): InvitationListResponse! @auth

  # Get user claim reward
  getClaimReward: ClaimRewardResponse! @auth

  # Get all infinite agent configs
  infiniteAgentConfigs: InfiniteAgentConfigsResponse! @auth

  # Get infinite agent config by ID
  infiniteAgentConfig(id: ID!): InfiniteAgentConfigResponse! @auth

  # get ReferralTreeSnapshot and ReferralTreeNode
  referralTreeSnapshots: ReferralTreeSnapshotsResponse! @auth

  # get ReferralTreeSnapshot and ReferralTreeNode by id
  referralTreeSnapshot(id: ID!): ReferralTreeSnapshotResponse! @auth

  # get InfiniteAgentReferralTree and InfiniteAgentTreeNode
  infiniteAgentReferralTrees: InfiniteAgentReferralTreesResponse! @auth

  # get InfiniteAgentReferralTree and InfiniteAgentTreeNode by id
  infiniteAgentReferralTree(id: ID!): InfiniteAgentReferralTreeResponse! @auth
}

type Mutation {
  # Create user with referral
  createUserWithReferral(input: CreateUserWithReferralInput!): CreateUserResponse! @auth

  # Create user invitation code
  createUserInvitationCode(input: CreateUserInvitationCodeInput!): CreateUserResponse! @auth

  # Update commission rates and meme fee rebate for a specific level
  updateLevelCommission(input: UpdateLevelCommissionInput!): UpdateLevelCommissionResponse! @auth

  # Claim activity cashback rewards (old - replaced by new activity cashback system)
  # claimActivityCashback(input: ClaimActivityCashbackInput!): ClaimResultResponse! @auth

  # Claim agent referral rewards
  claimAgentReferral(input: ClaimAgentReferralInput!): ClaimResultResponse! @auth

  # Create infinite agent config
  createInfiniteAgentConfig(input: CreateInfiniteAgentConfigInput!): CreateInfiniteAgentConfigResponse! @auth

  # Update infinite agent config
  updateInfiniteAgentConfig(input: UpdateInfiniteAgentConfigInput!): UpdateInfiniteAgentConfigResponse! @auth

  # Create referral tree snapshot manually
  createReferralTreeSnapshot: CreateReferralTreeSnapshotResponse! @auth

  # Create infinite agent tree
  createInfiniteAgentReferralTrees: CreateInfiniteAgentReferralTreeResponse! @auth
}


