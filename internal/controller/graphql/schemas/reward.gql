type InvitationRecord {
  address: String!
  transactionVolume: Float!
  invitedWithdrawal: Float!
  date: String!
}

type InvitationRecordResponse {
  success: Boolean!
  message: String!
  data: [InvitationRecord!]!
}

# WithdrawalRecord represents a single withdrawal record
type WithdrawalRecord {
  hash: String!
  withdrawalReward: String!
  date: String!
}

# WithdrawalRecordResponse represents the response for withdrawal records query
type WithdrawalRecordResponse {
  data: [WithdrawalRecord!]!
  total: Int!
  page: Int!
  pageSize: Int!
  success: Boolean!
  message: String
}
