package resolvers

import (
	"context"
	"fmt"
	"sort"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/invitation"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/reward"
)

type RewardResolver struct {
	rewardService           *reward.RewardDataService
	invitationRecordService *invitation.InvitationRecordService
}

func NewRewardResolver() *RewardResolver {
	return &RewardResolver{
		rewardService:           reward.NewRewardDataService(),
		invitationRecordService: invitation.NewInvitationRecordService(),
	}
}

func (r *RewardResolver) InvitationRecords(ctx context.Context) (*gql_model.InvitationRecordResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	invitationRecords, err := r.invitationRecordService.GetInvitationRecords(ctx, userID)
	if err != nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to obtain invitation record: %v", err),
		}, nil
	}

	var gqlRecords []*gql_model.InvitationRecord
	for _, record := range invitationRecords {
		// transactionVolume, _ := record.AddressTransactionVolume.Float64()
		// handlingFee, _ := record.AddressHandlingFee.Float64()
		fmt.Println("record", record)
		gqlRecord := &gql_model.InvitationRecord{}
		gqlRecords = append(gqlRecords, gqlRecord)
	}

	return &gql_model.InvitationRecordResponse{
		Success: true,
		Message: "Get invitation record successfully",
		Data:    gqlRecords,
	}, nil
}

func (r *RewardResolver) WithdrawalRecords(ctx context.Context) (*gql_model.WithdrawalRecordResponse, error) {
	// Create repository instance
	activityCashbackRepo := transaction.NewActivityCashbackRepository()

	// Get current user ID (from authentication info in context)
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		message := "Failed to get user information"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     1,
			PageSize: 10,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Query claimed activity cashback records
	claimedCashbacks, err := activityCashbackRepo.GetClaimedCashbacksByUserID(ctx, userID)
	if err != nil {
		message := "Failed to query activity cashback records"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     1,
			PageSize: 10,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Query claimed commission records
	claimedCommissions, err := r.getClaimedCommissionsByUserID(ctx, userID)
	if err != nil {
		message := "Failed to query commission records"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     1,
			PageSize: 10,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Convert record format
	var withdrawalRecords []*gql_model.WithdrawalRecord

	// Convert activity cashback records
	for _, cashback := range claimedCashbacks {
		// Generate hash value (using ID as unique identifier)
		hash := fmt.Sprintf("01x35...%s", cashback.ID.String()[:8])

		// Format reward amount (including currency symbol)
		var withdrawalReward string
		if cashback.CashbackAmountSOL.GreaterThan(decimal.Zero) {
			withdrawalReward = cashback.CashbackAmountSOL.String()
		} else {
			withdrawalReward = cashback.CashbackAmountUSD.String()
		}

		// Format date
		var date string
		if cashback.ClaimedAt != nil {
			date = cashback.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		withdrawalRecords = append(withdrawalRecords, &gql_model.WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: withdrawalReward,
			Date:             date,
		})
	}

	for _, commission := range claimedCommissions {
		// TODO: Process commission record
		hash := fmt.Sprintf("01x35...%s", commission.ID.String()[:8])

		var withdrawalReward string
		if commission.CommissionAsset == "SOL" {
			withdrawalReward = commission.CommissionAmount.String()
		} else {
			withdrawalReward = commission.CommissionAmount.String()
		}

		var date string
		if commission.ClaimedAt != nil {
			date = commission.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		withdrawalRecords = append(withdrawalRecords, &gql_model.WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: withdrawalReward,
			Date:             date,
		})
	}

	sort.Slice(withdrawalRecords, func(i, j int) bool {
		return withdrawalRecords[i].Date > withdrawalRecords[j].Date
	})

	message := "Query successful"
	return &gql_model.WithdrawalRecordResponse{
		Data:     withdrawalRecords,
		Total:    len(withdrawalRecords),
		Page:     1,
		PageSize: len(withdrawalRecords),
		Success:  true,
		Message:  &message,
	}, nil
}

// getClaimedCommissionsByUserID Query user's claimed commission records
func (r *RewardResolver) getClaimedCommissionsByUserID(ctx context.Context, userID uuid.UUID) ([]model.CommissionLedger, error) {
	// Directly query database to get claimed commission records
	var commissions []model.CommissionLedger
	err := global.GVA_DB.WithContext(ctx).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Preload("RecipientUser").
		Find(&commissions).Error

	if err != nil {
		return nil, fmt.Errorf("Failed to query commission records: %w", err)
	}

	return commissions, nil
}
