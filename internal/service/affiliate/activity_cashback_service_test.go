package affiliate

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
)

// Mock repositories
type MockAffiliateRepository struct {
	mock.Mock
}

func (m *MockAffiliateRepository) CreateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error {
	args := m.Called(ctx, tx)
	return args.Error(0)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionByOrderID(ctx context.Context, orderID uuid.UUID) (*model.AffiliateTransaction, error) {
	args := m.Called(ctx, orderID)
	return args.Get(0).(*model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionByTxHash(ctx context.Context, txHash string) (*model.AffiliateTransaction, error) {
	args := m.Called(ctx, txHash)
	return args.Get(0).(*model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) UpdateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error {
	args := m.Called(ctx, tx)
	return args.Error(0)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionsByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	args := m.Called(ctx, userID, limit, offset)
	return args.Get(0).([]model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionsByReferrerID(ctx context.Context, referrerID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	args := m.Called(ctx, referrerID, limit, offset)
	return args.Get(0).([]model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) GetUnpaidCommissions(ctx context.Context, referrerID uuid.UUID) ([]model.AffiliateTransaction, error) {
	args := m.Called(ctx, referrerID)
	return args.Get(0).([]model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) MarkCommissionAsPaid(ctx context.Context, transactionIDs []uint) error {
	args := m.Called(ctx, transactionIDs)
	return args.Error(0)
}

func (m *MockAffiliateRepository) UpsertSolPriceSnapshot(ctx context.Context, snapshot *model.SolPriceSnapshot) error {
	args := m.Called(ctx, snapshot)
	return args.Error(0)
}

func (m *MockAffiliateRepository) GetLatestSolPrice(ctx context.Context) (*model.SolPriceSnapshot, error) {
	args := m.Called(ctx)
	return args.Get(0).(*model.SolPriceSnapshot), args.Error(1)
}

func (m *MockAffiliateRepository) GetSolPriceHistory(ctx context.Context, from, to time.Time) ([]model.SolPriceSnapshot, error) {
	args := m.Called(ctx, from, to)
	return args.Get(0).([]model.SolPriceSnapshot), args.Error(1)
}

func (m *MockAffiliateRepository) GetUserTotalVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockAffiliateRepository) GetReferrerTotalCommission(ctx context.Context, referrerID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, referrerID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockAffiliateRepository) GetReferrerUnpaidCommission(ctx context.Context, referrerID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, referrerID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetDirectReferrals(ctx context.Context, userID uuid.UUID) ([]model.User, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.User), args.Error(1)
}

func (m *MockUserRepository) GetAllDownlineUsers(ctx context.Context, userID uuid.UUID, maxDepth int) ([]uuid.UUID, error) {
	args := m.Called(ctx, userID, maxDepth)
	return args.Get(0).([]uuid.UUID), args.Error(1)
}

func (m *MockUserRepository) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.Referral), args.Error(1)
}

func (m *MockUserRepository) GetInvitationCountByUserIDAndPeriod(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (int, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Int(0), args.Error(1)
}

func (m *MockUserRepository) GetUserWalletAddress(ctx context.Context, userID uuid.UUID) (string, error) {
	args := m.Called(ctx, userID)
	return args.String(0), args.Error(1)
}

type MockLevelRepository struct {
	mock.Mock
}

func (m *MockLevelRepository) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepository) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepository) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	args := m.Called(ctx, level)
	return args.Error(0)
}

type MockActivityCashbackRepository struct {
	mock.Mock
}

func (m *MockActivityCashbackRepository) CreateActivityCashback(ctx context.Context, cashback *model.ActivityCashback) error {
	args := m.Called(ctx, cashback)
	return args.Error(0)
}

func (m *MockActivityCashbackRepository) GetActivityCashbackByID(ctx context.Context, id uuid.UUID) (*model.ActivityCashback, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.ActivityCashback), args.Error(1)
}

func (m *MockActivityCashbackRepository) GetActivityCashbackByTransactionID(ctx context.Context, transactionID uint) (*model.ActivityCashback, error) {
	args := m.Called(ctx, transactionID)
	return args.Get(0).(*model.ActivityCashback), args.Error(1)
}

func (m *MockActivityCashbackRepository) GetActivityCashbacksByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashback, error) {
	args := m.Called(ctx, userID, limit, offset)
	return args.Get(0).([]model.ActivityCashback), args.Error(1)
}

func (m *MockActivityCashbackRepository) GetPendingActivityCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.ActivityCashback, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.ActivityCashback), args.Error(1)
}

func (m *MockActivityCashbackRepository) UpdateActivityCashbackStatus(ctx context.Context, id uuid.UUID, status string) error {
	args := m.Called(ctx, id, status)
	return args.Error(0)
}

func (m *MockActivityCashbackRepository) MarkActivityCashbackAsClaimed(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockActivityCashbackRepository) GetTotalActivityCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockActivityCashbackRepository) GetPendingActivityCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

// TestActivityCashbackService is a test version that doesn't use global logger
type TestActivityCashbackService struct {
	affiliateRepo        repo.AffiliateRepositoryInterface
	userRepo             transaction.UserRepositoryInterface
	levelRepo            repo.LevelRepo
	activityCashbackRepo repo.ActivityCashbackRepositoryInterface
	logger               *zap.Logger
}

func NewTestActivityCashbackService(logger *zap.Logger) *TestActivityCashbackService {
	return &TestActivityCashbackService{
		logger: logger,
	}
}

func (s *TestActivityCashbackService) ProcessActivityTransactionCashback(ctx context.Context, affiliateTx *model.AffiliateTransaction) error {
	s.logger.Info("Processing activity transaction cashback",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("status", string(affiliateTx.Status)))

	// Only process completed transactions
	if affiliateTx.Status != model.StatusCompleted {
		s.logger.Debug("Skipping non-completed transaction",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("status", string(affiliateTx.Status)))
		return nil
	}

	// Check if cashback already exists for this transaction
	existingCashback, err := s.activityCashbackRepo.GetActivityCashbackByTransactionID(ctx, affiliateTx.ID)
	if err == nil && existingCashback != nil {
		s.logger.Debug("Cashback already exists for transaction",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("cashback_id", existingCashback.ID.String()))
		return nil
	}

	user, err := s.getUserWithLevel(ctx, affiliateTx.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user with level: %w", err)
	}

	solPrice, err := s.getSolPriceForTransaction(ctx, affiliateTx)
	if err != nil {
		return fmt.Errorf("failed to get SOL price: %w", err)
	}

	// ActivityFeeRate * QuoteAmount * SolPriceSnapshot.Price = feeRate
	feeRate := solPrice.Price.Mul(affiliateTx.QuoteAmount)
	cashbackAmountUSD := user.AgentLevel.MemeFeeRebate.Mul(feeRate)

	cashbackAmountSOL := decimal.Zero
	if !solPrice.Price.IsZero() {
		cashbackAmountSOL = cashbackAmountUSD.Div(solPrice.Price)
	}

	s.logger.Info("Calculated activity cashback",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("user_level", user.AgentLevel.Name),
		zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
		zap.String("sol_price", solPrice.Price.String()),
		zap.String("fee_rate", feeRate.String()),
		zap.String("activity_fee_rate", user.AgentLevel.MemeFeeRate.String()),
		zap.String("activity_fee_rebate", user.AgentLevel.MemeFeeRebate.String()),
		zap.String("cashback_amount_usd", cashbackAmountUSD.String()),
		zap.String("cashback_amount_sol", cashbackAmountSOL.String()))

	cashback := &model.ActivityCashback{
		UserID:                 affiliateTx.UserID,
		UserAddress:            affiliateTx.UserAddress,
		Status:                 "PENDING_CLAIM",
		AffiliateTransactionID: affiliateTx.ID,
		SolPriceUSD:            solPrice.Price,
		CashbackAmountUSD:      cashbackAmountUSD,
		CashbackAmountSOL:      cashbackAmountSOL,
		CreatedAt:              &time.Time{},
	}

	now := time.Now()
	cashback.CreatedAt = &now

	if err := s.activityCashbackRepo.CreateActivityCashback(ctx, cashback); err != nil {
		return fmt.Errorf("failed to create activity cashback record: %w", err)
	}

	s.logger.Info("Activity transaction cashback processed successfully",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("cashback_id", cashback.ID.String()),
		zap.String("total_cashback_usd", cashbackAmountUSD.String()),
		zap.String("total_cashback_sol", cashbackAmountSOL.String()))

	return nil
}

func (s *TestActivityCashbackService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	return &model.User{
		ID: userID,
		AgentLevel: model.AgentLevel{
			ID:            3,
			Name:          "Lv3",
			MemeFeeRate:   decimal.NewFromFloat(0.009), // 0.9%
			MemeFeeRebate: decimal.NewFromFloat(0.10),  // 10% activity cashback
		},
	}, nil
}

func (s *TestActivityCashbackService) getSolPriceForTransaction(ctx context.Context, affiliateTx *model.AffiliateTransaction) (*model.SolPriceSnapshot, error) {
	return &model.SolPriceSnapshot{
		ID:        1,
		Price:     decimal.NewFromFloat(100.0), // $100/SOL
		Timestamp: affiliateTx.CreatedAt,
	}, nil
}

func TestMemeRebateService_ProcessMemeTransactionRebate(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	tests := []struct {
		name             string
		affiliateTx      *model.AffiliateTransaction
		user             *model.User
		solPrice         *model.SolPriceSnapshot
		expectedError    bool
		expectedCashback *model.ActivityCashback
		setupMocks       func(*MockAffiliateRepository, *MockUserRepository, *MockLevelRepository, *MockActivityCashbackRepository)
	}{
		{
			name: "成功处理活动交易返现 - Lv3代理",
			affiliateTx: &model.AffiliateTransaction{
				ID:          1,
				OrderID:     uuid.New(),
				UserID:      uuid.New(),
				UserAddress: "test_address",
				Status:      model.StatusCompleted,
				QuoteAmount: decimal.NewFromFloat(1000.0), // $1000交易
				CreatedAt:   time.Now(),
			},
			user: &model.User{
				ID: uuid.New(),
				AgentLevel: model.AgentLevel{
					ID:            3,
					Name:          "Lv3",
					MemeFeeRate:   decimal.NewFromFloat(0.009), // 0.9%
					MemeFeeRebate: decimal.NewFromFloat(0.10),  // 10% activity cashback
				},
			},
			solPrice: &model.SolPriceSnapshot{
				ID:        1,
				Price:     decimal.NewFromFloat(100.0), // $100/SOL
				Timestamp: time.Now(),
			},
			expectedError: false,
			expectedCashback: &model.ActivityCashback{
				Status:            "PENDING_CLAIM",
				SolPriceUSD:       decimal.NewFromFloat(100.0),
				CashbackAmountUSD: decimal.NewFromFloat(0.9),   // $1000 * 0.9% * 10% = $0.9
				CashbackAmountSOL: decimal.NewFromFloat(0.009), // $0.9 / $100 = 0.009 SOL
			},
			setupMocks: func(mockAffiliate *MockAffiliateRepository, mockUser *MockUserRepository, mockLevel *MockLevelRepository, mockCashback *MockActivityCashbackRepository) {
				mockCashback.On("GetActivityCashbackByTransactionID", mock.Anything, uint(1)).Return((*model.ActivityCashback)(nil), assert.AnError)

				mockCashback.On("CreateActivityCashback", mock.Anything, mock.AnythingOfType("*model.ActivityCashback")).Return(nil)
			},
		},
		{
			name: "跳过非完成状态的交易",
			affiliateTx: &model.AffiliateTransaction{
				ID:          2,
				OrderID:     uuid.New(),
				UserID:      uuid.New(),
				UserAddress: "test_address",
				Status:      model.StatusPending,
				QuoteAmount: decimal.NewFromFloat(1000.0),
				CreatedAt:   time.Now(),
			},
			user: &model.User{
				ID: uuid.New(),
				AgentLevel: model.AgentLevel{
					ID:            3,
					Name:          "Lv3",
					MemeFeeRate:   decimal.NewFromFloat(0.009),
					MemeFeeRebate: decimal.NewFromFloat(0.10),
				},
			},
			solPrice: &model.SolPriceSnapshot{
				ID:        1,
				Price:     decimal.NewFromFloat(100.0),
				Timestamp: time.Now(),
			},
			expectedError:    false,
			expectedCashback: nil,
			setupMocks: func(mockAffiliate *MockAffiliateRepository, mockUser *MockUserRepository, mockLevel *MockLevelRepository, mockCashback *MockActivityCashbackRepository) {
			},
		},
		{
			name: "跳过已存在返现记录的交易",
			affiliateTx: &model.AffiliateTransaction{
				ID:          3,
				OrderID:     uuid.New(),
				UserID:      uuid.New(),
				UserAddress: "test_address",
				Status:      model.StatusCompleted,
				QuoteAmount: decimal.NewFromFloat(1000.0),
				CreatedAt:   time.Now(),
			},
			user: &model.User{
				ID: uuid.New(),
				AgentLevel: model.AgentLevel{
					ID:            3,
					Name:          "Lv3",
					MemeFeeRate:   decimal.NewFromFloat(0.009),
					MemeFeeRebate: decimal.NewFromFloat(0.10),
				},
			},
			solPrice: &model.SolPriceSnapshot{
				ID:        1,
				Price:     decimal.NewFromFloat(100.0),
				Timestamp: time.Now(),
			},
			expectedError:    false,
			expectedCashback: nil, // 不应该创建新的返现记录
			setupMocks: func(mockAffiliate *MockAffiliateRepository, mockUser *MockUserRepository, mockLevel *MockLevelRepository, mockCashback *MockActivityCashbackRepository) {
				existingCashback := &model.ActivityCashback{
					ID:                     uuid.New(),
					AffiliateTransactionID: 3,
				}
				mockCashback.On("GetActivityCashbackByTransactionID", mock.Anything, uint(3)).Return(existingCashback, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockAffiliate := &MockAffiliateRepository{}
			mockUser := &MockUserRepository{}
			mockLevel := &MockLevelRepository{}
			mockCashback := &MockActivityCashbackRepository{}

			if tt.setupMocks != nil {
				tt.setupMocks(mockAffiliate, mockUser, mockLevel, mockCashback)
			}

			service := NewTestActivityCashbackService(logger)
			service.affiliateRepo = mockAffiliate
			service.userRepo = mockUser
			service.levelRepo = mockLevel
			service.activityCashbackRepo = mockCashback

			err := service.ProcessActivityTransactionCashback(context.Background(), tt.affiliateTx)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mockAffiliate.AssertExpectations(t)
			mockUser.AssertExpectations(t)
			mockLevel.AssertExpectations(t)
			mockCashback.AssertExpectations(t)
		})
	}
}

func TestActivityCashbackService_CalculateCashbackAmount(t *testing.T) {
	tests := []struct {
		name                string
		quoteAmount         decimal.Decimal
		solPrice            decimal.Decimal
		activityFeeRate     decimal.Decimal
		activityFeeRebate   decimal.Decimal
		expectedFeeRate     decimal.Decimal
		expectedCashbackUSD decimal.Decimal
		expectedCashbackSOL decimal.Decimal
	}{
		{
			name:                "Lv3代理 $1000交易返现计算",
			quoteAmount:         decimal.NewFromFloat(1000.0), // $1000
			solPrice:            decimal.NewFromFloat(100.0),  // $100/SOL
			activityFeeRate:     decimal.NewFromFloat(0.009),  // 0.9%
			activityFeeRebate:   decimal.NewFromFloat(0.10),   // 10%
			expectedFeeRate:     decimal.NewFromFloat(9.0),    // $1000 * 0.9% = $9
			expectedCashbackUSD: decimal.NewFromFloat(0.9),    // $9 * 10% = $0.9
			expectedCashbackSOL: decimal.NewFromFloat(0.009),  // $0.9 / $100 = 0.009 SOL
		},
		{
			name:                "Lv1代理 $500交易返现计算",
			quoteAmount:         decimal.NewFromFloat(500.0),    // $500
			solPrice:            decimal.NewFromFloat(120.0),    // $120/SOL
			activityFeeRate:     decimal.NewFromFloat(0.009),    // 0.9%
			activityFeeRebate:   decimal.NewFromFloat(0.05),     // 5%
			expectedFeeRate:     decimal.NewFromFloat(4.5),      // $500 * 0.9% = $4.5
			expectedCashbackUSD: decimal.NewFromFloat(0.225),    // $4.5 * 5% = $0.225
			expectedCashbackSOL: decimal.NewFromFloat(0.001875), // $0.225 / $120 = 0.001875 SOL
		},
		{
			name:                "零SOL价格处理",
			quoteAmount:         decimal.NewFromFloat(1000.0),
			solPrice:            decimal.Zero,
			activityFeeRate:     decimal.NewFromFloat(0.009),
			activityFeeRebate:   decimal.NewFromFloat(0.10),
			expectedFeeRate:     decimal.NewFromFloat(9.0),
			expectedCashbackUSD: decimal.NewFromFloat(0.9),
			expectedCashbackSOL: decimal.Zero,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// ActivityFeeRate * QuoteAmount = feeRate
			feeRate := tt.activityFeeRate.Mul(tt.quoteAmount)
			assert.True(t, feeRate.Equal(tt.expectedFeeRate),
				"费用率计算错误: 期望 %s, 实际 %s", tt.expectedFeeRate.String(), feeRate.String())

			// ActivityFeeRebate * feeRate = cashbackAmountUSD
			cashbackAmountUSD := tt.activityFeeRebate.Mul(feeRate)
			assert.True(t, cashbackAmountUSD.Equal(tt.expectedCashbackUSD),
				"返现金额计算错误: 期望 %s, 实际 %s", tt.expectedCashbackUSD.String(), cashbackAmountUSD.String())

			// 计算SOL等价返现金额
			cashbackAmountSOL := decimal.Zero
			if !tt.solPrice.IsZero() {
				cashbackAmountSOL = cashbackAmountUSD.Div(tt.solPrice)
			}
			assert.True(t, cashbackAmountSOL.Equal(tt.expectedCashbackSOL),
				"SOL返现金额计算错误: 期望 %s, 实际 %s", tt.expectedCashbackSOL.String(), cashbackAmountSOL.String())
		})
	}
}

func TestActivityCashbackService_GetSolPriceForTransaction(t *testing.T) {
	t.Run("测试SOL价格查询逻辑", func(t *testing.T) {
		mockAffiliate := &MockAffiliateRepository{}
		mockUser := &MockUserRepository{}
		mockLevel := &MockLevelRepository{}
		mockCashback := &MockActivityCashbackRepository{}

		logger, _ := zap.NewDevelopment()
		service := NewTestActivityCashbackService(logger)
		service.affiliateRepo = mockAffiliate
		service.userRepo = mockUser
		service.levelRepo = mockLevel
		service.activityCashbackRepo = mockCashback

		assert.NotNil(t, service)
		assert.NotNil(t, service.affiliateRepo)
		assert.NotNil(t, service.userRepo)
		assert.NotNil(t, service.levelRepo)
		assert.NotNil(t, service.activityCashbackRepo)
	})
}
