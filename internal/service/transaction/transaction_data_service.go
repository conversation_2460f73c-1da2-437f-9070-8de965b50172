package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// TransactionDataServiceInterface defines the interface for transaction data operations
type TransactionDataServiceInterface interface {
	GetTransactionData(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) ([]*response.TransactionData, error)
}

// TransactionDataService implements transaction data operations
type TransactionDataService struct {
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo  transaction.CommissionLedgerRepositoryInterface
	userRepo        transaction.UserRepositoryInterface
}

// NewTransactionDataService creates a new transaction data service
func NewTransactionDataService() TransactionDataServiceInterface {
	return &TransactionDataService{
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:  transaction.NewCommissionLedgerRepository(),
		userRepo:        transaction.NewUserRepository(),
	}
}

// GetTransactionData retrieves transaction data based on the specified data type and time range
func (s *TransactionDataService) GetTransactionData(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) ([]*response.TransactionData, error) {
	// Get all direct referrals (level 1)
	directReferrals, err := s.userRepo.GetDirectReferrals(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get direct referrals", zap.Error(err))
		return nil, fmt.Errorf("failed to get direct referrals: %w", err)
	}

	// Get all downline users (up to 3 levels)
	allDownlineUsers, err := s.userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Error("Failed to get downline users", zap.Error(err))
		return nil, fmt.Errorf("failed to get downline users: %w", err)
	}

	// Get transacting user count
	transactingUserCount, err := s.getTransactingUserCount(ctx, allDownlineUsers)
	if err != nil {
		global.GVA_LOG.Error("Failed to get transacting user count", zap.Error(err))
		return nil, fmt.Errorf("failed to get transacting user count: %w", err)
	}

	// Calculate time range
	startTime, endTime, err := s.calculateTimeRange(timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate time range: %w", err)
	}

	var transactionAmountUsd float64
	var claimedUsd float64
	var pendingClaimUsd float64

	switch dataType {
	case "ALL":
		// Calculate total transaction amount from affiliate transactions (MEME)
		totalMemeAmount, err := s.affiliateRepo.GetVolumeByUserIDsAndPeriod(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get total MEME amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get total MEME amount: %w", err)
		}

		// Calculate total transaction amount from hyperliquid transactions (Contract)
		totalContractAmount, err := s.hyperLiquidRepo.GetVolumeByUserIDsAndPeriod(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get total contract amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get total contract amount: %w", err)
		}

		// Get claimed commission amount for the time period
		claimedAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "ALL", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get claimed amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get claimed amount: %w", err)
		}

		// Get pending claim amount for the time period
		pendingClaimAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "ALL", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get pending claim amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get pending claim amount: %w", err)
		}

		totalAmount := totalMemeAmount.Add(totalContractAmount)
		transactionAmountUsd, _ = totalAmount.Float64()
		claimedUsd, _ = claimedAmount.Float64()
		pendingClaimUsd, _ = pendingClaimAmount.Float64()

	case "MEME":
		// Calculate total MEME transaction amount for the time period
		totalMemeAmount, err := s.affiliateRepo.GetVolumeByUserIDsAndPeriod(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get total MEME amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get total MEME amount: %w", err)
		}

		// Get claimed MEME commission amount for the time period
		claimedMemeAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get claimed MEME amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get claimed MEME amount: %w", err)
		}

		// Get pending claim MEME amount for the time period
		pendingClaimMemeAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get pending claim MEME amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get pending claim MEME amount: %w", err)
		}

		transactionAmountUsd, _ = totalMemeAmount.Float64()
		claimedUsd, _ = claimedMemeAmount.Float64()
		pendingClaimUsd, _ = pendingClaimMemeAmount.Float64()

	case "CONTRACT":
		// Calculate total contract transaction amount for the time period
		totalContractAmount, err := s.hyperLiquidRepo.GetVolumeByUserIDsAndPeriod(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get total contract amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get total contract amount: %w", err)
		}

		// Get claimed contract commission amount for the time period
		claimedContractAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get claimed contract amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get claimed contract amount: %w", err)
		}

		// Get pending claim contract amount for the time period
		pendingClaimContractAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get pending claim contract amount", zap.Error(err))
			return nil, fmt.Errorf("failed to get pending claim contract amount: %w", err)
		}

		transactionAmountUsd, _ = totalContractAmount.Float64()
		claimedUsd, _ = claimedContractAmount.Float64()
		pendingClaimUsd, _ = pendingClaimContractAmount.Float64()

	default:
		return nil, fmt.Errorf("unsupported data type: %s", dataType)
	}

	// Get invitation count for the time period
	invitationCount, err := s.userRepo.GetInvitationCountByUserIDAndPeriod(ctx, userID, startTime, endTime)
	if err != nil {
		global.GVA_LOG.Error("Failed to get invitation count", zap.Error(err))
		// Use fallback to total direct referrals if period-specific count fails
		invitationCount = len(directReferrals)
	}

	// Create transaction data
	result := &response.TransactionData{
		TransactionAmountUsd: transactionAmountUsd,
		ClaimedUsd:           claimedUsd,
		PendingClaimUsd:      pendingClaimUsd,
		InvitationCount:      invitationCount,
		TransactingUserCount: transactingUserCount,
	}

	return []*response.TransactionData{result}, nil
}

// calculateTimeRange calculates the start and end time based on the time range string
func (s *TransactionDataService) calculateTimeRange(timeRange string) (time.Time, time.Time, error) {
	now := time.Now().UTC()
	var startTime time.Time

	switch timeRange {
	case "TODAY":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	case "LAST_30_DAYS":
		startTime = now.AddDate(0, 0, -30)
	case "LAST_60_DAYS":
		startTime = now.AddDate(0, 0, -60)
	case "ALL_TIME":
		startTime = time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC) // 设置一个足够早的开始时间
	default:
		return time.Time{}, time.Time{}, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	return startTime, now, nil
}

// getTransactingUserCount calculates the number of users who have made transactions
func (s *TransactionDataService) getTransactingUserCount(ctx context.Context, userIDs []uuid.UUID) (int, error) {
	if len(userIDs) == 0 {
		return 0, nil
	}

	// Get users who have made MEME transactions
	memeTransactingUsers, err := s.affiliateRepo.GetTransactingUserIDs(ctx, userIDs)
	if err != nil {
		return 0, fmt.Errorf("failed to get MEME transacting users: %w", err)
	}

	// Get users who have made contract transactions
	contractTransactingUsers, err := s.hyperLiquidRepo.GetTransactingUserIDs(ctx, userIDs)
	if err != nil {
		return 0, fmt.Errorf("failed to get contract transacting users: %w", err)
	}

	// Combine and deduplicate user IDs
	allTransactingUsers := make(map[uuid.UUID]bool)
	for _, userID := range memeTransactingUsers {
		allTransactingUsers[userID] = true
	}
	for _, userID := range contractTransactingUsers {
		allTransactingUsers[userID] = true
	}

	return len(allTransactingUsers), nil
}
