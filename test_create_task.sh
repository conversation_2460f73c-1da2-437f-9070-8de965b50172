#!/bin/bash

# Test the createTask GraphQL mutation with the fixed sortOrder issue

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

# GraphQL endpoint (from config.yaml)
GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

# Test mutation without sortOrder (this should work now with our fix)
echo "Testing createTask mutation without sortOrder..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation CreateTask { createTask(input: { categoryId: \"123456\" name: \"Task 1\" description: \"First task\" taskType: ONE_TIME frequency: ONE_TIME points: 1 maxCompletions: 10 }) { id categoryId name description taskType frequency points maxCompletions resetPeriod conditions actionTarget verificationMethod externalLink isActive startDate endDate sortOrder createdAt updatedAt category { id name displayName description icon sortOrder isActive createdAt updatedAt tasks { id categoryId name description taskType frequency points maxCompletions resetPeriod conditions actionTarget verificationMethod externalLink isActive startDate endDate sortOrder createdAt updatedAt category { id name displayName description icon sortOrder isActive createdAt updatedAt tasks { id categoryId name description taskType frequency points maxCompletions resetPeriod conditions actionTarget verificationMethod externalLink isActive startDate endDate sortOrder createdAt updatedAt } } } } userProgress { id userId taskId status progressValue targetValue completionCount pointsEarned lastCompletedAt lastResetAt streakCount metadata createdAt updatedAt progressPercentage canBeClaimed task { id categoryId name description taskType frequency points maxCompletions resetPeriod conditions actionTarget verificationMethod externalLink isActive startDate endDate sortOrder createdAt updatedAt } } } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n\n"

# Test mutation with sortOrder (this should also work)
echo "Testing createTask mutation with sortOrder..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation CreateTask { createTask(input: { categoryId: \"123456\" name: \"Task 2\" description: \"Second task\" taskType: ONE_TIME frequency: ONE_TIME points: 2 maxCompletions: 5 sortOrder: 1 }) { id name sortOrder } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n"
