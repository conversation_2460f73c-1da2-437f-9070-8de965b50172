# XBIT Activity Cashback GraphQL API

## API 接口

### GraphQL Endpoint
```
POST /api/dex-agent/graphql
```

### GraphQL Playground
```
GET /api/dex-agent/graphql/playground
```

### 健康检查
```
GET /api/dex-agent/graphql/ping
GET /api/dex-agent/graphql/healthz
```

## 认证

### JWT Token 认证
所有需要认证的操作都需要在请求头中包含 JWT Token：

```http
Authorization: Bearer <jwt_token>
```

## 目录

1. [用户活动返现](#用户活动返现)
   - [用户仪表板](#用户仪表板)
   - [任务中心](#任务中心)
   - [用户等级信息](#用户等级信息)
   - [任务进度](#任务进度)
   - [任务完成历史](#任务完成历史)
   - [任务分类](#任务分类)
   - [按分类获取任务](#按分类获取任务)

2. [任务操作](#任务操作)
   - [完成任务](#完成任务)
   - [领取任务奖励](#领取任务奖励)
   - [领取返现](#领取返现)
   - [刷新任务列表](#刷新任务列表)

3. [管理员功能](#管理员功能)
   - [任务管理](#任务管理)
   - [等级福利管理](#等级福利管理)
   - [统计信息](#统计信息)
   - [系统维护](#系统维护)

4. [数据类型定义](#数据类型定义)
   - [任务相关类型](#任务相关类型)
   - [用户相关类型](#用户相关类型)
   - [响应类型](#响应类型)

## 用户活动返现

### 用户仪表板
获取用户的活动返现仪表板信息，包括等级信息、可领取返现、最近领取记录等。

```graphql
query ActivityCashbackDashboard {
  activityCashbackDashboard {
    success
    message
    data {
      userTierInfo {
        userId
        currentTier
        totalPoints
        pointsThisMonth
        tradingVolumeUsd
        activeDaysThisMonth
        cumulativeCashbackUsd
        claimableCashbackUsd
        claimedCashbackUsd
        lastActivityDate
        tierUpgradedAt
        monthlyResetAt
        userRank
        tierBenefit {
          id
          tierLevel
          tierName
          minPoints
          cashbackPercentage
          benefitsDescription
          tierColor
          tierIcon
          isActive
        }
      }
      tierBenefit {
        id
        tierLevel
        tierName
        minPoints
        cashbackPercentage
        benefitsDescription
        tierColor
        tierIcon
        isActive
      }
      nextTier {
        id
        tierLevel
        tierName
        minPoints
        cashbackPercentage
        benefitsDescription
        tierColor
        tierIcon
        isActive
      }
      pointsToNextTier
      claimableCashback
      userRank
      recentClaims {
        id
        userId
        claimType
        totalAmountUsd
        totalAmountSol
        transactionHash
        status
        claimedAt
        processedAt
        metadata
      }
    }
  }
}
```

**返回示例**:
```json
{
  "data": {
    "activityCashbackDashboard": {
      "success": true,
      "message": "Dashboard data retrieved successfully",
      "data": {
        "userTierInfo": {
          "userId": "123e4567-e89b-12d3-a456-426614174000",
          "currentTier": 2,
          "totalPoints": 1500,
          "pointsThisMonth": 500,
          "tradingVolumeUsd": 25000.50,
          "activeDaysThisMonth": 15,
          "cumulativeCashbackUsd": 125.75,
          "claimableCashbackUsd": 25.50,
          "claimedCashbackUsd": 100.25,
          "lastActivityDate": "2025-01-15T10:30:00Z",
          "tierUpgradedAt": "2025-01-10T08:00:00Z",
          "monthlyResetAt": "2025-02-01T00:00:00Z",
          "userRank": 45,
          "tierBenefit": {
            "id": "2",
            "tierLevel": 2,
            "tierName": "Silver",
            "minPoints": 1000,
            "cashbackPercentage": 0.05,
            "benefitsDescription": "Silver tier benefits",
            "tierColor": "#C0C0C0",
            "tierIcon": "silver-icon.png",
            "isActive": true
          }
        },
        "tierBenefit": {
          "id": "2",
          "tierLevel": 2,
          "tierName": "Silver",
          "minPoints": 1000,
          "cashbackPercentage": 0.05,
          "benefitsDescription": "Silver tier benefits",
          "tierColor": "#C0C0C0",
          "tierIcon": "silver-icon.png",
          "isActive": true
        },
        "nextTier": {
          "id": "3",
          "tierLevel": 3,
          "tierName": "Gold",
          "minPoints": 5000,
          "cashbackPercentage": 0.08,
          "benefitsDescription": "Gold tier benefits",
          "tierColor": "#FFD700",
          "tierIcon": "gold-icon.png",
          "isActive": true
        },
        "pointsToNextTier": 3500,
        "claimableCashback": 25.50,
        "userRank": 45,
        "recentClaims": [
          {
            "id": "456e7890-e89b-12d3-a456-426614174001",
            "userId": "123e4567-e89b-12d3-a456-426614174000",
            "claimType": "TRADING_CASHBACK",
            "totalAmountUsd": 25.50,
            "totalAmountSol": 0.255,
            "transactionHash": "0x123...abc",
            "status": "COMPLETED",
            "claimedAt": "2025-01-14T15:30:00Z",
            "processedAt": "2025-01-14T15:35:00Z",
            "metadata": "{\"source\":\"trading\"}"
          }
        ]
      }
    }
  }
}
```

### 任务中心
获取任务中心信息，包括所有任务分类、用户进度、今日完成情况等。

```graphql
query TaskCenter {
  taskCenter {
    success
    message
    data {
      categories {
        category {
          id
          name
          displayName
          description
          icon
          sortOrder
          isActive
        }
        tasks {
          task {
            id
            categoryId
            name
            description
            taskType
            frequency
            taskIdentifier
            points
            maxCompletions
            resetPeriod
            conditions
            actionTarget
            verificationMethod
            externalLink
            isActive
            startDate
            endDate
            sortOrder
          }
          progress {
            id
            userId
            taskId
            status
            progressValue
            targetValue
            completionCount
            pointsEarned
            lastCompletedAt
            lastResetAt
            streakCount
            metadata
            progressPercentage
            canBeClaimed
          }
        }
      }
      userProgress {
        id
        userId
        taskId
        status
        progressValue
        targetValue
        completionCount
        pointsEarned
        lastCompletedAt
        lastResetAt
        streakCount
        metadata
        progressPercentage
        canBeClaimed
      }
      completedToday
      pointsEarnedToday
      streakTasks {
        id
        userId
        taskId
        status
        progressValue
        targetValue
        completionCount
        pointsEarned
        lastCompletedAt
        lastResetAt
        streakCount
        metadata
        progressPercentage
        canBeClaimed
      }
    }
  }
}
```

### 用户等级信息
获取用户当前的等级信息。

```graphql
query UserTierInfo {
  userTierInfo {
    userId
    currentTier
    totalPoints
    pointsThisMonth
    tradingVolumeUsd
    activeDaysThisMonth
    cumulativeCashbackUsd
    claimableCashbackUsd
    claimedCashbackUsd
    lastActivityDate
    tierUpgradedAt
    monthlyResetAt
    userRank
    tierBenefit {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      benefitsDescription
      tierColor
      tierIcon
      isActive
    }
  }
}
```

### 任务进度
获取用户的所有任务进度。

```graphql
query UserTaskProgress {
  userTaskProgress {
    success
    message
    data {
      id
      userId
      taskId
      status
      progressValue
      targetValue
      completionCount
      pointsEarned
      lastCompletedAt
      lastResetAt
      streakCount
      metadata
      progressPercentage
      canBeClaimed
      task {
        id
        categoryId
        name
        description
        taskType
        frequency
        taskIdentifier
        points
        maxCompletions
        resetPeriod
        conditions
        actionTarget
        verificationMethod
        externalLink
        isActive
        startDate
        endDate
        sortOrder
      }
    }
  }
}
```

### 任务完成历史
获取用户的任务完成历史记录。

```graphql
query TaskCompletionHistory($input: TaskCompletionHistoryInput) {
  taskCompletionHistory(input: $input) {
    success
    message
    data {
      id
      userId
      taskId
      pointsAwarded
      completionDate
      verificationData
      createdAt
      task {
        id
        name
        description
        taskType
        frequency
        points
      }
    }
    total
  }
}
```

**输入参数**:
```graphql
input TaskCompletionHistoryInput {
  taskId: ID
  startDate: Time
  endDate: Time
  limit: Int
  offset: Int
}
```

### 任务分类
获取所有任务分类。

```graphql
query TaskCategories {
  taskCategories {
    id
    name
    displayName
    description
    icon
    sortOrder
    isActive
    createdAt
    updatedAt
    tasks {
      id
      name
      description
      taskType
      frequency
      points
      isActive
    }
  }
}
```

### 按分类获取任务
根据分类名称获取任务列表。

```graphql
query TasksByCategory($categoryName: String!) {
  tasksByCategory(categoryName: $categoryName) {
    id
    categoryId
    name
    description
    taskType
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    isActive
    startDate
    endDate
    sortOrder
    category {
      id
      name
      displayName
      description
      icon
      sortOrder
      isActive
    }
  }
}
```

## 任务操作

### 完成任务
完成指定的任务。

```graphql
mutation CompleteTask($input: CompleteTaskInput!) {
  completeTask(input: $input) {
    success
    message
    pointsAwarded
    newTierLevel
    tierUpgraded
  }
}
```

**输入参数**:
```graphql
input CompleteTaskInput {
  taskId: ID!
  verificationData: String # JSON string
}
```

**返回示例**:
```json
{
  "data": {
    "completeTask": {
      "success": true,
      "message": "Task completed successfully",
      "pointsAwarded": 100,
      "newTierLevel": 3,
      "tierUpgraded": true
    }
  }
}
```

### 领取任务奖励
领取已完成任务的奖励。

```graphql
mutation ClaimTaskReward($input: ClaimTaskRewardInput!) {
  claimTaskReward(input: $input) {
    success
    message
    pointsClaimed
  }
}
```

**输入参数**:
```graphql
input ClaimTaskRewardInput {
  taskId: ID!
}
```

### 领取返现
领取可用的返现金额。

```graphql
mutation ClaimCashback($input: ClaimCashbackInput!) {
  claimCashback(input: $input) {
    success
    message
    claimId
    amountUsd
    amountSol
  }
}
```

**输入参数**:
```graphql
input ClaimCashbackInput {
  amountUsd: Float!
}
```

**返回示例**:
```json
{
  "data": {
    "claimCashback": {
      "success": true,
      "message": "Cashback claim created successfully",
      "claimId": "789e0123-e89b-12d3-a456-426614174002",
      "amountUsd": 25.50,
      "amountSol": 0.255
    }
  }
}
```

### 刷新任务列表
刷新用户的任务列表。

```graphql
mutation RefreshTaskList {
  refreshTaskList
}
```

## 管理员功能

### 任务管理

#### 创建任务
```graphql
mutation CreateTask($input: CreateTaskInput!) {
  createTask(input: $input) {
    id
    categoryId
    name
    description
    taskType
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    isActive
    startDate
    endDate
    sortOrder
    createdAt
    updatedAt
  }
}
```

#### 更新任务
```graphql
mutation UpdateTask($input: UpdateTaskInput!) {
  updateTask(input: $input) {
    id
    categoryId
    name
    description
    taskType
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    isActive
    startDate
    endDate
    sortOrder
    createdAt
    updatedAt
  }
}
```

#### 删除任务
```graphql
mutation DeleteTask($taskId: ID!) {
  deleteTask(taskId: $taskId)
}
```

#### 获取所有任务
```graphql
query AdminGetAllTasks {
  adminGetAllTasks {
    id
    categoryId
    name
    description
    taskType
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    isActive
    startDate
    endDate
    sortOrder
    createdAt
    updatedAt
  }
}
```

### 等级福利管理

#### 创建等级福利
```graphql
mutation CreateTierBenefit($input: CreateTierBenefitInput!) {
  createTierBenefit(input: $input) {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      benefitsDescription
      tierColor
      tierIcon
      isActive
      createdAt
      updatedAt
    }
  }
}
```

#### 更新等级福利
```graphql
mutation UpdateTierBenefit($input: UpdateTierBenefitInput!) {
  updateTierBenefit(input: $input) {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      benefitsDescription
      tierColor
      tierIcon
      isActive
      createdAt
      updatedAt
    }
  }
}
```

#### 删除等级福利
```graphql
mutation DeleteTierBenefit($tierBenefitId: ID!) {
  deleteTierBenefit(tierBenefitId: $tierBenefitId)
}
```

#### 获取所有等级福利
```graphql
query TierBenefits {
  tierBenefits {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      benefitsDescription
      tierColor
      tierIcon
      isActive
      createdAt
      updatedAt
    }
  }
}
```

### 统计信息

#### 任务完成统计
```graphql
query AdminGetTaskCompletionStats($input: AdminStatsInput!) {
  adminGetTaskCompletionStats(input: $input) {
    success
    message
    data {
      taskCompletions {
        taskName
        completionCount
      }
      startDate
      endDate
      totalTasks
    }
  }
}
```

#### 用户活动统计
```graphql
query AdminGetUserActivityStats($input: AdminStatsInput!) {
  adminGetUserActivityStats(input: $input) {
    success
    message
    data {
      dailyCompletions {
        date
        completionCount
      }
      startDate
      endDate
    }
  }
}
```

#### 等级分布统计
```graphql
query AdminGetTierDistribution {
  adminGetTierDistribution {
    success
    message
    data {
      tierLevel
      userCount
    }
  }
}
```

#### 获取顶级用户
```graphql
query AdminGetTopUsers($limit: Int) {
  adminGetTopUsers(limit: $limit) {
    userId
    email
    totalPoints
    availableCashback
    totalCashbackClaimed
    lastActivityAt
    createdAt
    currentTier {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      isActive
    }
  }
}
```

### 系统维护

#### 重置每日任务
```graphql
mutation AdminResetDailyTasks {
  adminResetDailyTasks
}
```

#### 重置每周任务
```graphql
mutation AdminResetWeeklyTasks {
  adminResetWeeklyTasks
}
```

#### 重置每月任务
```graphql
mutation AdminResetMonthlyTasks {
  adminResetMonthlyTasks
}
```

#### 重新计算所有用户等级
```graphql
mutation AdminRecalculateAllUserTiers {
  adminRecalculateAllUserTiers
}
```

#### 初始化任务数据
```graphql
mutation AdminSeedInitialTasks {
  adminSeedInitialTasks
}
```

## 数据类型定义

### 任务相关类型

```graphql
enum TaskType {
  DAILY
  ONE_TIME
  UNLIMITED
  PROGRESSIVE
  MANUAL_UPDATE
}

enum TaskFrequency {
  DAILY
  ONE_TIME
  UNLIMITED
  PROGRESSIVE
  MANUAL
}

enum TaskIdentifier {
  # Daily Tasks
  DAILY_CHECKIN
  MEME_TRADE_DAILY
  PERPETUAL_TRADE_DAILY
  MARKET_PAGE_VIEW
  CONSECUTIVE_CHECKIN
  CONSECUTIVE_TRADING_DAYS

  # Community Tasks
  TWITTER_FOLLOW
  TWITTER_RETWEET
  TWITTER_LIKE
  TELEGRAM_JOIN
  INVITE_FRIENDS
  SHARE_REFERRAL

  # Trading Tasks
  TRADING_POINTS
  ACCUMULATED_TRADING_10K
  ACCUMULATED_TRADING_50K
  ACCUMULATED_TRADING_100K
  ACCUMULATED_TRADING_500K
}

enum TaskStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  CLAIMED
  EXPIRED
}

type TaskCategory {
  id: ID!
  name: String!
  displayName: String!
  description: String
  icon: String
  sortOrder: Int!
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
  tasks: [ActivityTask!]
}

type ActivityTask {
  id: ID!
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  isActive: Boolean!
  startDate: Time
  endDate: Time
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
  category: TaskCategory
  userProgress: UserTaskProgress
}

type UserTaskProgress {
  id: ID!
  userId: ID!
  taskId: ID!
  status: TaskStatus!
  progressValue: Int!
  targetValue: Int
  completionCount: Int!
  pointsEarned: Int!
  lastCompletedAt: Time
  lastResetAt: Time
  streakCount: Int!
  metadata: String # JSON string
  createdAt: Time!
  updatedAt: Time!
  task: ActivityTask
  progressPercentage: Float!
  canBeClaimed: Boolean!
}
```

### 用户相关类型

```graphql
type UserTierInfo {
  userId: ID!
  currentTier: Int!
  totalPoints: Int!
  pointsThisMonth: Int!
  tradingVolumeUsd: Float!
  activeDaysThisMonth: Int!
  cumulativeCashbackUsd: Float!
  claimableCashbackUsd: Float!
  claimedCashbackUsd: Float!
  lastActivityDate: Time
  tierUpgradedAt: Time
  monthlyResetAt: Time
  createdAt: Time!
  updatedAt: Time!
  tierBenefit: TierBenefit
  userRank: Int
}

type TierBenefit {
  id: ID!
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type ActivityCashbackClaim {
  id: ID!
  userId: ID!
  claimType: ClaimType!
  totalAmountUsd: Float!
  totalAmountSol: Float!
  transactionHash: String
  status: ClaimStatus!
  claimedAt: Time!
  processedAt: Time
  metadata: String # JSON string
  createdAt: Time!
  updatedAt: Time!
}

enum ClaimType {
  TRADING_CASHBACK
  TASK_REWARD
  TIER_BONUS
  REFERRAL_BONUS
}

enum ClaimStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
```

### 响应类型

```graphql
type UserDashboard {
  userTierInfo: UserTierInfo!
  tierBenefit: TierBenefit!
  nextTier: TierBenefit
  pointsToNextTier: Int!
  claimableCashback: Float!
  recentClaims: [ActivityCashbackClaim!]!
  userRank: Int!
}

type TaskCenter {
  categories: [TaskCategoryWithTasks!]!
  userProgress: [UserTaskProgress!]!
  completedToday: Int!
  pointsEarnedToday: Int!
  streakTasks: [UserTaskProgress!]!
}

type TaskCategoryWithTasks {
  category: TaskCategory!
  tasks: [TaskWithProgress!]!
}

type TaskWithProgress {
  task: ActivityTask!
  progress: UserTaskProgress
}

type TaskCompletionHistory {
  id: ID!
  userId: ID!
  taskId: ID!
  pointsAwarded: Int!
  completionDate: Time!
  verificationData: String # JSON string
  createdAt: Time!
  task: ActivityTask
}

# Response Types
type UserDashboardResponse {
  success: Boolean!
  message: String!
  data: UserDashboard
}

type TaskCenterResponse {
  success: Boolean!
  message: String!
  data: TaskCenter
}

type TaskCompletionResponse {
  success: Boolean!
  message: String!
  pointsAwarded: Int!
  newTierLevel: Int
  tierUpgraded: Boolean!
}

type TaskClaimResponse {
  success: Boolean!
  message: String!
  pointsClaimed: Int!
}

type CashbackClaimResponse {
  success: Boolean!
  message: String!
  claimId: ID!
  amountUsd: Float!
  amountSol: Float!
}

type TierBenefitsResponse {
  success: Boolean!
  message: String!
  data: [TierBenefit!]!
}

type UserTaskProgressResponse {
  success: Boolean!
  message: String!
  data: [UserTaskProgress!]!
}

type TaskCompletionHistoryResponse {
  success: Boolean!
  message: String!
  data: [TaskCompletionHistory!]!
  total: Int!
}
```

### 输入类型

```graphql
input CompleteTaskInput {
  taskId: ID!
  verificationData: String # JSON string
}

input ClaimTaskRewardInput {
  taskId: ID!
}

input ClaimCashbackInput {
  amountUsd: Float!
}

input CreateTaskInput {
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  startDate: Time
  endDate: Time
  sortOrder: Int
}

input UpdateTaskInput {
  id: ID!
  categoryId: ID
  name: String
  description: String
  taskType: TaskType
  frequency: TaskFrequency
  points: Int
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  isActive: Boolean
  startDate: Time
  endDate: Time
  sortOrder: Int
}

input CreateTierBenefitInput {
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
}

input UpdateTierBenefitInput {
  id: ID!
  tierLevel: Int
  tierName: String
  minPoints: Int
  cashbackPercentage: Float
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean
}

input TaskCompletionHistoryInput {
  taskId: ID
  startDate: Time
  endDate: Time
  limit: Int
  offset: Int
}

input AdminStatsInput {
  startDate: Time!
  endDate: Time!
}
```

## 错误处理

所有API调用都可能返回以下错误：

- **认证错误**: 当JWT token无效或过期时
- **权限错误**: 当用户没有执行特定操作的权限时
- **验证错误**: 当输入参数不符合要求时
- **业务逻辑错误**: 当操作违反业务规则时
- **系统错误**: 当发生内部服务器错误时

错误响应格式：
```json
{
  "errors": [
    {
      "message": "Error description",
      "extensions": {
        "code": "ERROR_CODE",
        "field": "fieldName"
      }
    }
  ]
}
```

## 使用示例

### 完整的用户流程示例

1. **获取用户仪表板**
```graphql
query {
  activityCashbackDashboard {
    success
    message
    data {
      userTierInfo {
        currentTier
        totalPoints
        claimableCashbackUsd
      }
      claimableCashback
    }
  }
}
```

2. **查看任务中心**
```graphql
query {
  taskCenter {
    success
    message
    data {
      completedToday
      pointsEarnedToday
      categories {
        category {
          name
          displayName
        }
        tasks {
          task {
            name
            points
            taskType
          }
          progress {
            status
            progressPercentage
            canBeClaimed
          }
        }
      }
    }
  }
}
```

3. **完成任务**
```graphql
mutation {
  completeTask(input: {
    taskId: "task-uuid-here"
    verificationData: "{\"source\":\"manual\"}"
  }) {
    success
    message
    pointsAwarded
    tierUpgraded
  }
}
```

4. **领取返现**
```graphql
mutation {
  claimCashback(input: {
    amountUsd: 25.50
  }) {
    success
    message
    claimId
    amountUsd
    amountSol
  }
}
```

## 注意事项

1. **认证要求**: 所有操作都需要有效的JWT token
2. **权限控制**: 管理员功能需要相应的管理员权限
3. **数据验证**: 输入数据会进行严格验证
4. **错误处理**: 请妥善处理所有可能的错误情况
5. **性能考虑**: 大量数据查询建议使用分页和限制参数
6. **实时性**: 某些数据可能需要刷新才能获取最新状态
