# XBIT Agent GraphQL API

## API 接口

### GraphQL Endpoint
```
POST /api/dex-agent/graphql
```

### GraphQL Playground
```
GET /api/dex-agent/graphql/playground
```

### 健康检查
```
GET /api/dex-agent/graphql/ping
GET /api/dex-agent/graphql/healthz
```

## 认证

### JWT Token 认证
所有需要认证的操作都需要在请求头中包含 JWT Token：

```http
Authorization: Bearer <jwt_token>
```

## 目录

1. [节点代理](#节点代理)

   - [邀请返佣](#邀请返佣)
   - [数据总览](#数据总览)
   - [交易总览](#交易总览)
   - [所有代理等级](#所有代理等级)
   - [通过id获取代理等级](#通过id获取代理等级)

2. [受邀用户](#受邀用户)

   - [受邀用户总览](#受邀用户总览)
   - [邀请列表](#邀请列表)

3. [代理奖励](#代理奖励)

   - [邀请奖励](#邀请奖励)
   - [提取记录](#提取记录)

4. [推荐关系](#推荐关系)

   - [推荐快照查询](#推荐快照查询)
   - [创建推荐关系](#创建推荐关系)
   - [创建用户邀请码](#创建用户邀请码)
   - [更新等级佣金](#更新等级佣金)

5. [无限代理](#无限代理)

   - [查询所有无限代理](#查询所有无限代理)
   - [通过id查询无限代理](#通过id查询无限代理)
   - [创建无限代理](#创建无限代理)
   - [更新无限代理](#更新无限代理)

6. [数据类型定义](#数据类型定义)

   - [用户相关类型](#用户相关类型)
   - [推荐快照类型](#推荐快照类型)
   - [无限代理类型](#无限代理类型)
   - [响应类型](#响应类型)

## 节点代理

### 邀请返佣
```
query UserLevelInfo {
    userLevelInfo {
        success
        message
        data {
            memeVolume
            contractVolume
            totalVolume
            currentLevel {
                id
                name
                memeVolumeThreshold
                contractVolumeThreshold
                memeFeeRate
                takerFeeRate
                makerFeeRate
                directCommissionRate
                indirectCommissionRate
                extendedCommissionRate
                memeFeeRebate
            }
        }
    }
}
```
返回
```
{
    "data": {
        "userLevelInfo": {
            "success": true,
            "message": "Successfully retrieved user level information",
            "data": {
                "memeVolume": 0,
                "contractVolume": 0,
                "totalVolume": 0,
                "currentLevel": {
                    "id": 1,
                    "name": "Lv1",
                    "memeVolumeThreshold": 0,
                    "contractVolumeThreshold": 0,
                    "memeFeeRate": 0.009,
                    "takerFeeRate": 0.0001,
                    "makerFeeRate": 0.0001,
                    "directCommissionRate": 0.1,
                    "indirectCommissionRate": 0.03,
                    "extendedCommissionRate": 0.01,
                    "memeFeeRebate": 0
                }
            }
        }
    }
}
```

### 数据总览
```
query DataOverview {
    dataOverview(input: { dataType: MEME, timeRange: ONE_DAY }) {
        success
        message
        data {
            timestamp
            period
            rebateAmount {
                all
                meme
                contract
            }
            transactionVolume {
                all
                meme
                contract
            }
            invitationCount {
                all
                meme
                contract
            }
        }
        summary {
            totalRebateAmount {
                all
                meme
                contract
            }
            totalTransactionVolume {
                all
                meme
                contract
            }
            totalInvitationCount {
                all
                meme
                contract
            }
            peakRebateAmount {
                all
                meme
                contract
            }
            peakTransactionVolume {
                all
                meme
                contract
            }
            peakInvitationCount {
                all
                meme
                contract
            }
            averageRebateAmount {
                all
                meme
                contract
            }
            averageTransactionVolume {
                all
                meme
                contract
            }
            averageInvitationCount {
                all
                meme
                contract
            }
        }
    }
}

```
返回
```
{
    "data": {
        "dataOverview": {
            "success": true,
            "message": null,
            "data": [
                {
                    "timestamp": "2025-08-31T08:51:52.053714Z",
                    "period": "08:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T09:51:52.053714Z",
                    "period": "09:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T10:51:52.053714Z",
                    "period": "10:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T11:51:52.053714Z",
                    "period": "11:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T12:51:52.053714Z",
                    "period": "12:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T13:51:52.053714Z",
                    "period": "13:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T14:51:52.053714Z",
                    "period": "14:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T15:51:52.053714Z",
                    "period": "15:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T16:51:52.053714Z",
                    "period": "16:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T17:51:52.053714Z",
                    "period": "17:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T18:51:52.053714Z",
                    "period": "18:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T19:51:52.053714Z",
                    "period": "19:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T20:51:52.053714Z",
                    "period": "20:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T21:51:52.053714Z",
                    "period": "21:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T22:51:52.053714Z",
                    "period": "22:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-08-31T23:51:52.053714Z",
                    "period": "23:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-09-01T00:51:52.053714Z",
                    "period": "00:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-09-01T01:51:52.053714Z",
                    "period": "01:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-09-01T02:51:52.053714Z",
                    "period": "02:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-09-01T03:51:52.053714Z",
                    "period": "03:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-09-01T04:51:52.053714Z",
                    "period": "04:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-09-01T05:51:52.053714Z",
                    "period": "05:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-09-01T06:51:52.053714Z",
                    "period": "06:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                },
                {
                    "timestamp": "2025-09-01T07:51:52.053714Z",
                    "period": "07:51",
                    "rebateAmount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "transactionVolume": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    },
                    "invitationCount": {
                        "all": 0,
                        "meme": 0,
                        "contract": 0
                    }
                }
            ],
            "summary": {
                "totalRebateAmount": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                },
                "totalTransactionVolume": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                },
                "totalInvitationCount": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                },
                "peakRebateAmount": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                },
                "peakTransactionVolume": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                },
                "peakInvitationCount": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                },
                "averageRebateAmount": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                },
                "averageTransactionVolume": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                },
                "averageInvitationCount": {
                    "all": 0,
                    "meme": 0,
                    "contract": 0
                }
            }
        }
    }
}
```
### 交易总览
```
query TransactionData {
    transactionData(input: { dataType: MEME, timeRange: TODAY }) {
        success
        message
        transactionData {
            transactionAmountUsd
            claimedUsd
            pendingClaimUsd
            invitationCount
            transactingUserCount
        }
    }
}

```
返回
```
{
    "data": {
        "transactionData": {
            "success": true,
            "message": null,
            "transactionData": [
                {
                    "transactionAmountUsd": 0,
                    "claimedUsd": 0,
                    "pendingClaimUsd": 0,
                    "invitationCount": 0,
                    "transactingUserCount": 0
                }
            ]
        }
    }
}
```

### 所有代理等级
```
query AgentLevels {
    agentLevels {
        id
        name
        memeVolumeThreshold
        contractVolumeThreshold
        memeFeeRate
        takerFeeRate
        makerFeeRate
        directCommissionRate
        indirectCommissionRate
        extendedCommissionRate
        memeFeeRebate
    }
}
```
返回
```
{
    "data": {
        "agentLevels": [
            {
                "id": 1,
                "name": "Lv1",
                "memeVolumeThreshold": 0,
                "contractVolumeThreshold": 0,
                "memeFeeRate": 0.009,
                "takerFeeRate": 0.0001,
                "makerFeeRate": 0.0001,
                "directCommissionRate": 0.1,
                "indirectCommissionRate": 0.03,
                "extendedCommissionRate": 0.01,
                "memeFeeRebate": 0
            },
            {
                "id": 2,
                "name": "Lv2",
                "memeVolumeThreshold": 10000,
                "contractVolumeThreshold": 900000,
                "memeFeeRate": 0.009,
                "takerFeeRate": 0.0001,
                "makerFeeRate": 0.0001,
                "directCommissionRate": 0.2,
                "indirectCommissionRate": 0.04,
                "extendedCommissionRate": 0.02,
                "memeFeeRebate": 0.05
            },
            {
                "id": 3,
                "name": "Lv3",
                "memeVolumeThreshold": 30000,
                "contractVolumeThreshold": 2700000,
                "memeFeeRate": 0.009,
                "takerFeeRate": 0.0001,
                "makerFeeRate": 0.0001,
                "directCommissionRate": 0.3,
                "indirectCommissionRate": 0.05,
                "extendedCommissionRate": 0.025,
                "memeFeeRebate": 0.1
            },
            {
                "id": 4,
                "name": "Lv4",
                "memeVolumeThreshold": 100000,
                "contractVolumeThreshold": 9000000,
                "memeFeeRate": 0.009,
                "takerFeeRate": 0.0001,
                "makerFeeRate": 0.0001,
                "directCommissionRate": 0.4,
                "indirectCommissionRate": 0.06,
                "extendedCommissionRate": 0.03,
                "memeFeeRebate": 0.15
            },
            {
                "id": 5,
                "name": "Lv5",
                "memeVolumeThreshold": 300000,
                "contractVolumeThreshold": 27000000,
                "memeFeeRate": 0.009,
                "takerFeeRate": 0.0001,
                "makerFeeRate": 0.0001,
                "directCommissionRate": 0.5,
                "indirectCommissionRate": 0.07,
                "extendedCommissionRate": 0.04,
                "memeFeeRebate": 0.2
            },
            {
                "id": 6,
                "name": "Lv∞",
                "memeVolumeThreshold": 1000000,
                "contractVolumeThreshold": 90000000,
                "memeFeeRate": 0.009,
                "takerFeeRate": 0.0001,
                "makerFeeRate": 0.0001,
                "directCommissionRate": 0.6,
                "indirectCommissionRate": 0.08,
                "extendedCommissionRate": 0.05,
                "memeFeeRebate": 0.25
            },
            {
                "id": 7,
                "name": "Lv∞",
                "memeVolumeThreshold": 30000000,
                "contractVolumeThreshold": 270000000,
                "memeFeeRate": 0.009,
                "takerFeeRate": 0.0001,
                "makerFeeRate": 0.0001,
                "directCommissionRate": 0.7,
                "indirectCommissionRate": 0.09,
                "extendedCommissionRate": 0.06,
                "memeFeeRebate": 0.3
            }
        ]
    }
}
```
### 通过id获取代理等级
```
query AgentLevel {
    agentLevel(id: 6) {
        id
        name
        memeVolumeThreshold
        contractVolumeThreshold
        memeFeeRate
        takerFeeRate
        makerFeeRate
        directCommissionRate
        indirectCommissionRate
        extendedCommissionRate
        memeFeeRebate
    }
}

```
返回
```
{
    "data": {
        "agentLevel": {
            "id": 6,
            "name": "Lv∞",
            "memeVolumeThreshold": 1000000,
            "contractVolumeThreshold": 90000000,
            "memeFeeRate": 0.009,
            "takerFeeRate": 0.0001,
            "makerFeeRate": 0.0001,
            "directCommissionRate": 0.6,
            "indirectCommissionRate": 0.08,
            "extendedCommissionRate": 0.05,
            "memeFeeRebate": 0.25
        }
    }
}
```
## 受邀用户

### 受邀用户总览
```
query InvitationSummary {
    invitationSummary {
        success
        message
        data {
            invitedUserCount
            tradingUserCount
        }
    }
}

```
返回
```
{
    "data": {
        "invitationSummary": {
            "success": true,
            "message": "Successfully obtained the invitation summary",
            "data": {
                "invitedUserCount": 0,
                "tradingUserCount": 0
            }
        }
    }
}
```

### 邀请列表
```
query InvitationList {
    invitationList(input: { transactionType: ALL, page: 10, pageSize: 20 }) {
        total
        page
        pageSize
        success
        message
        data {
            userAddress
            invitationTime
            transactionType
            transactionAmount
            accumulatedCommission
        }
    }
}

```
返回
```
{
    "data": {
        "invitationList": {
            "total": 0,
            "page": 10,
            "pageSize": 20,
            "success": true,
            "message": "Successfully obtained the invitation list",
            "data": []
        }
    }
}
```

## 代理奖励

### 邀请奖励
```
query InvitationRecords {
    invitationRecords {
        success
        message
        data {
            address
            transactionVolume
            invitedWithdrawal
            date
        }
    }
}
```
返回
```
{
    "data": {
        "invitationRecords": {
            "success": true,
            "message": "Get invitation record successfully",
            "data": []
        }
    }
}
```
### 提取记录
```
query WithdrawalRecords {
    withdrawalRecords {
        total
        page
        pageSize
        success
        message
        data {
            hash
            withdrawalReward
            date
        }
    }
}
```
返回
```
{
    "data": {
        "withdrawalRecords": {
            "total": 1,
            "page": 1,
            "pageSize": 1,
            "success": true,
            "message": "Success",
            "data": [
                {
                    "hash": "01x35...30123ebc",
                    "withdrawalReward": "0.3",
                    "date": "08-29"
                }
            ]
        }
    }
}
```

## 推荐关系

### 推荐快照查询

```graphql
query GetReferralSnapshot {
  referralSnapshot {
    userId
    directCount
    totalDownlineCount
    totalVolumeUsd
    totalRewardsDistributed
    l1UplineId
    l2UplineId
    l3UplineId
    user {
      id
      email
      invitationCode
    }
    l1Upline {
      id
      email
    }
    l2Upline {
      id
      email
    }
    l3Upline {
      id
      email
    }
  }
}
```

**返回类型**: `ReferralSnapshot`


### 创建推荐关系

创建新用户并建立推荐关系。

```graphql
mutation CreateUserWithReferral($input: CreateUserWithReferralInput!) {
  createUserWithReferral(input: $input) {
    user {
      id
      email
      invitationCode
      createdAt
      agentLevelId
    }
    success
    message
  }
}
```

**输入参数**:

```graphql
input CreateUserWithReferralInput {
  invitationCode: String! # 邀请码
}
```

**返回类型**: `CreateUserResponse`

### 创建用户邀请码

```graphql
mutation CreateUserInvitationCode($input: CreateUserInvitationCodeInput!) {
  createUserInvitationCode(input: $input) {
    user {
      id
      email
      invitationCode
      createdAt
    }
    success
    message
  }
}
```

**输入参数**:

```graphql
input CreateUserInvitationCodeInput {
  invitationCode: String! # 邀请码
  email: String # 邮箱（可选）
  chain: String # 链名称（可选）
  name: String # 名称（可选）
  walletAddress: String # 钱包地址（可选）
  walletId: ID # 钱包ID（可选）
  walletAccountId: ID # 钱包账户ID（可选）
  walletType: WalletType! # 钱包类型
}
```

### 更新等级佣金

更新特定等级的佣金率和返利设置。

```graphql
mutation UpdateLevelCommission($input: UpdateLevelCommissionInput!) {
  updateLevelCommission(input: $input) {
    level {
      id
      name
      directCommissionRate
      indirectCommissionRate
      extendedCommissionRate
      memeFeeRebate
    }
    success
    message
  }
}
```

**输入参数**:

```graphql
input UpdateLevelCommissionInput {
  levelId: Int! # 等级ID
  directCommissionRate: Float! # 直接佣金率
  indirectCommissionRate: Float! # 间接佣金率
  extendedCommissionRate: Float! # 扩展佣金率
  memeFeeRebate: Float! # Meme费用返利
}
```

**枚举类型**:

- `WalletType`: EMBEDDED, MANAGED

**返回类型**: `CreateUserResponse`

## 无限代理

### 查询所有无限代理

```graphql
query {
  infiniteAgentConfigs {
    infiniteAgentConfigs {
      id
      userId
      createdAt
      updatedAt
      user {
        id
        email
      }
    }
    success
    message
  }
}
```

### 通过id查询无限代理

```graphql
query {
  infiniteAgentConfig(id: "123e4567-e89b-12d3-a456-426614174000") {
    infiniteAgentConfig {
      id
      userId
      commissionRateN
    }
    success
    message
  }
}
```

**返回类型**: `InvitationRecordResponse`

### 创建无限代理

```graphql
mutation {
  createInfiniteAgentConfig(
    input: {
      userId: "123e4567-e89b-12d3-a456-426614174000"
      commissionRateN: 0.15
    }
  ) {
    infiniteAgentConfig {
      id
      userId
    }
    success
    message
  }
}
```

###  更新无限代理


```graphql
mutation {
  updateInfiniteAgentConfig(
    input: {
      id: "123e4567-e89b-12d3-a456-426614174000"
      userId: "123e4567-e89b-12d3-a456-426614174001"
      commissionRateN: 0.20
    }
  ) {
    infiniteAgentConfig {
      id
      commissionRateN
    }
    success
    message
  }
}
```

**返回类型**: `UpdateLevelCommissionResponse`


## 数据类型定义

### 用户相关类型

```graphql
type User {
  id: ID!
  email: String
  invitationCode: String
  createdAt: String!
  updatedAt: String!
  deletedAt: String
  agentLevelId: Int!
  agentLevel: AgentLevel!
  levelGracePeriodStartedAt: String
  levelUpgradedAt: String
  firstTransactionAt: String
  referrals: [Referral!]!
  referralSnapshot: ReferralSnapshot
  referredUsers: [Referral!]!
}

type AgentLevel {
  id: Int!
  name: String!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  memeFeeRebate: Float!
}

type Referral {
  id: Int!
  userId: ID!
  referrerId: ID
  depth: Int!
  createdAt: String!
  user: User!
  referrer: User
}
```

### 推荐快照类型

```graphql
type ReferralSnapshot {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  totalVolumeUsd: Float!
  totalRewardsDistributed: Float!
  l1UplineId: ID
  l2UplineId: ID
  l3UplineId: ID
  user: User!
  l1Upline: User
  l2Upline: User
  l3Upline: User
}
```

### 响应类型

```graphql
type CreateUserResponse {
  user: User!
  success: Boolean!
  message: String!
}

type UpdateLevelCommissionResponse {
  level: AgentLevel
  success: Boolean!
  message: String!
}
```

### 无限代理类型

```graphql
type InfiniteAgentConfig {
  id: ID!
  userID: ID!
  commissionRateN: Float!
  createdAt: Time!
  updatedAt: Time!
  user: User
}

type CreateInfiniteAgentConfigResponse {
  infiniteAgentConfig: InfiniteAgentConfig
  success: Boolean!
  message: String!
}

type UpdateInfiniteAgentConfigResponse {
  infiniteAgentConfig: InfiniteAgentConfig
  success: Boolean!
  message: String!
}

type InfiniteAgentConfigsResponse {
  infiniteAgentConfigs: [InfiniteAgentConfig!]!
  success: Boolean!
  message: String!
}

type InfiniteAgentConfigResponse {
  infiniteAgentConfig: InfiniteAgentConfig
  success: Boolean!
  message: String!
}

```